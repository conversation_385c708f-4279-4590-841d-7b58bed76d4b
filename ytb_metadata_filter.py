import re
import json
import aiohttp
import asyncio
import random
import string
from datetime import datetime
from aiohttp import ClientTimeout, TCPConnector, BasicAuth
import logger

JSONL_FILE = "/root/novel/ytb_bozhu/all_result.jsonl"

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USERNAME_PREFIX = "5851b070f69-zone-adam-session-"
PROXY_PASSWORD = "t5gNbn29fwu2Rlhc"

def generate_random_session():
    """生成16位随机的大小写字母加数字"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=16))

def get_proxy_config():
    """获取代理配置"""
    session_id = generate_random_session()
    username = f"{PROXY_USERNAME_PREFIX}{session_id}"

    proxy_url = f"http://{username}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}"

    return {
        'proxy': proxy_url,
        'proxy_auth': <PERSON>Auth(username, PROXY_PASSWORD)
    }

def read_jsonl_file(file_path):
    """读取包含video_id和short_id的JSON文件"""
    video_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)

                        # 处理video_id数组
                        if 'video_id' in data and isinstance(data['video_id'], list):
                            for vid in data['video_id']:
                                video_info = {
                                    'video_id': vid,
                                    'type': 'video',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                        # 处理short_id数组
                        if 'short_id' in data and isinstance(data['short_id'], list):
                            for sid in data['short_id']:
                                video_info = {
                                    'video_id': sid,
                                    'type': 'short',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                    except json.JSONDecodeError as e:
                        logger.error(f"解析第{line_num}行JSON失败: {e}")
                        continue
        return video_list
    except FileNotFoundError:
        logger.error(f"文件不存在: {file_path}")
        return []
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        return []
    
# 通用年份解析函数
def parse_year(date_str):
    if not date_str:
        return None
    try:
        # ISO8601
        if "T" in date_str:
            return datetime.fromisoformat(date_str.replace("Z", "+00:00")).year
        # YYYY-MM-DD
        if re.match(r"^\d{4}-\d{2}-\d{2}$", date_str):
            return int(date_str.split("-")[0])
        # 中文格式 2015年2月18日
        match = re.search(r"(\d{4})年", date_str)
        if match:
            return int(match.group(1))
    except Exception:
        return None
    return None


async def fetch_video_info(session, video_id, proxy_config=None):
    url = f"https://www.youtube.com/watch?v={video_id}"
    try:
        # 如果有代理配置，使用代理
        if proxy_config:
            async with session.get(url, proxy=proxy_config['proxy']) as resp:
                text = await resp.text()
        else:
            async with session.get(url) as resp:
                text = await resp.text()
    except Exception as e:
        return {"video_id": video_id, "error": str(e)}

    match = re.search(r'ytInitialPlayerResponse\s*=\s*(\{.*?\});', text)
    if not match:
        return {"video_id": video_id, "error": "No playerResponse"}

    try:
        data = json.loads(match.group(1))
    except Exception as e:
        return {"video_id": video_id, "error": f"JSON parse error: {e}"}

    video_details = data.get("videoDetails", {})
    streaming_data = data.get("streamingData", {})
    formats = streaming_data.get("adaptiveFormats", [])

    title = video_details.get("title")
    author = video_details.get("author")

    # 发布时间兼容
    microformat = data.get("microformat", {}).get("playerMicroformatRenderer", {})
    publish_date = (
        microformat.get("publishDate")
        or microformat.get("uploadDate")
        or video_details.get("publishDate", {}).get("simpleText")
    )
    year = parse_year(publish_date)

    # 筛选满足条件
    results = []
    for fmt in formats:
        height = fmt.get("height", 0)
        bitrate = fmt.get("bitrate", 0)
        if height >= 1080 and bitrate >= 1500 and (year is None or year >= 2000):
            results.append({
                "video_id": video_id,
                # "title": title,
                # "author": author,
                "quality": f"{height}p",
                "year": year,
                "bitrate": bitrate
            })
    return results


async def process_videos(video_ids, output_file="filtered_videos.jsonl", concurrency=500, use_proxy=True):
    timeout = ClientTimeout(total=30)  # 增加超时时间，因为代理可能较慢
    connector = TCPConnector(limit=concurrency)
    sem = asyncio.Semaphore(concurrency)

    # 获取代理配置
    proxy_config = get_proxy_config() if use_proxy else None

    if use_proxy:
        logger.info(f"使用代理: {PROXY_HOST}:{PROXY_PORT}")
    else:
        logger.info("不使用代理")

    async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
        async def bound_fetch(vid):
            async with sem:
                return await fetch_video_info(session, vid, proxy_config)

        tasks = [asyncio.create_task(bound_fetch(vid)) for vid in video_ids]

        with open(output_file, "w", encoding="utf-8") as f:
            for task in asyncio.as_completed(tasks):
                result = await task
                if result and isinstance(result, list):
                    for item in result:
                        f.write(json.dumps(item, ensure_ascii=False) + "\n")


if __name__ == "__main__":
    # 示例：替换成你的视频ID列表
    video_ids = [
        "zTiyRbrrLzc",
        "dQw4w9WgXcQ",
        "kJQP7kiw5Fk"
    ]
    video_list = read_jsonl_file(JSONL_FILE)

    # 使用代理模式处理视频
    asyncio.run(process_videos(video_ids, use_proxy=True))
    print("✅ 异步筛选完成，结果写入 filtered_videos.jsonl")

